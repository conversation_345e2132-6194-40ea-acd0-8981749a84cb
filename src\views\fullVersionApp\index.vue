<template>
  <div class="full-version-app-container">
    <div class="full-version-header">
      <div class="header-logo">
        <div class="logo"></div>
      </div>
      <div class="header-title">获取完整版</div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {},
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
@import '@/css/common.sass';
.full-version-app-container{
  height: 100%;
  width: 375px;
  background-color: #fff;
  .full-version-header{
    background: #07c160;
      color: white;
      
    .header-logo{
      width: .6rem;
      height: .6rem;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .logo{
        
        width: .5rem;
        height: .5rem;
        background: url('@/assets/images/logo.png') no-repeat center / contain;
      }
    }
    .header-title{
      font-size: 0.16rem;
    }
  }
  
}
</style>
