const { defineConfig } = require('@vue/cli-service');
const path = require('path');
module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  pages: {
    fullVersionApp: {
      entry: 'src/views/fullVersionApp/index.js',
      template: './public/fullVersionApp.html',
      filename: 'fullVersionApp.html',
      title: '分享app',
    }
  },
  chainWebpack: config => {
    // 为每个页面动态添加通用样式和screenFitter
    const pages = module.exports.pages;
    Object.keys(pages).forEach(pageName => {
      // 为每个入口文件添加screenFitter.js
      config
        .entry(pageName)
        .add(path.resolve(__dirname, 'src/utils/screenFitterLoader.js'))
        .end();
      // 为每个入口文件添加index.css
      config
        .entry(pageName)
        .add(path.resolve(__dirname, 'src/css/main.css'))
        .end();
    });
  }
})
