const { defineConfig } = require('@vue/cli-service');
const path = require('path');
module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  pages: {
    fullVersionApp: {
      entry: 'src/views/fullVersionApp/index.js',
      template: './public/fullVersionApp.html',
      filename: 'fullVersionApp.html',
      title: '分享app',
    }
  },
  chainWebpack: config => {
    // 为每个页面动态添加通用样式和screenFitter
    const pages = module.exports.pages;
    Object.keys(pages).forEach(pageName => {
      console.log('处理页面:', pageName);
      config.plugin(`html-${pageName}`).tap(args => {
        if (!args[0].head) args[0].head = '';
        args[0].head += `
          
        `;
        return args;
      });

      // 为每个入口文件添加screenFitter.js
      config
        .entry(pageName)
        .add(path.resolve(__dirname, 'src/utils/screenFitterLoader.js'))
        .end();
    });
  }
})
